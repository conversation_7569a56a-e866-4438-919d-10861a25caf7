# 自定义Stage组件Context

这个Context提供了一个机制，允许用户自定义开发stage组件，并在工作台应用中注册和使用这些组件。

## 功能特性

- 支持注册自定义stage组件的处理函数
- 存储组件类型与对应处理函数的映射关系
- 当ChatAction的actionTypes为inPlatform时，自动查找并调用对应的处理函数
- 完善的错误处理机制
- 支持同步和异步处理函数

## 使用方法

### 1. 在应用中提供Context

```tsx
import {CustomStageProvider} from '@/contexts/CustomStageContext';

function App() {
    return (
        <CustomStageProvider>
            {/* 你的应用组件 */}
        </CustomStageProvider>
    );
}
```

### 2. 注册自定义stage组件处理函数

```tsx
import {useRegisterStageHandler} from '@/contexts/CustomStageContext';
import {ChatAction} from '@/types/staff/element';

const MyCustomStageHandler = async (action: ChatAction) => {
    console.log('处理自定义stage组件:', action);
    
    // 获取传递的参数
    const {stageParams} = action;
    
    // 执行自定义逻辑
    if (stageParams?.showAlert) {
        alert(stageParams.message || '默认消息');
    }
    
    // 可以是异步操作
    if (stageParams?.apiCall) {
        await fetch('/api/custom-action', {
            method: 'POST',
            body: JSON.stringify(stageParams),
        });
    }
};

function MyComponent() {
    // 注册处理函数
    useRegisterStageHandler('my-custom-stage', MyCustomStageHandler);
    
    return <div>我的组件</div>;
}
```

### 3. 在ChatAction中使用

```tsx
const action: ChatAction = {
    text: '执行自定义操作',
    actionTypes: ['inPlatform'], // 必须包含inPlatform
    stageType: 'my-custom-stage', // 指定stage类型
    stageParams: { // 传递给处理函数的参数
        showAlert: true,
        message: '这是一个自定义消息',
        apiCall: true,
        customData: {
            userId: 123,
            action: 'update'
        }
    }
};
```

### 4. 手动执行处理函数

```tsx
import {useCustomStage} from '@/contexts/CustomStageContext';

function MyComponent() {
    const {executeHandler, isHandlerRegistered} = useCustomStage();
    
    const handleClick = async () => {
        if (!isHandlerRegistered('my-custom-stage')) {
            console.warn('处理函数未注册');
            return;
        }
        
        const result = await executeHandler('my-custom-stage', {
            text: '手动执行',
            stageType: 'my-custom-stage',
            stageParams: {
                message: '手动执行的消息'
            }
        });
        
        if (result.success) {
            console.log('执行成功:', result.data);
        } else {
            console.error('执行失败:', result.error);
        }
    };
    
    return <button onClick={handleClick}>手动执行</button>;
}
```

## API 参考

### CustomStageProvider

提供自定义stage组件Context的Provider组件。

**Props:**
- `children: ReactNode` - 子组件

### useCustomStage()

获取自定义stage组件Context的hook。

**返回值:**
- `registerHandler(stageType: string, handler: StageComponentHandler): void` - 注册处理函数
- `unregisterHandler(stageType: string): void` - 取消注册处理函数
- `executeHandler(stageType: string, action: ChatAction): Promise<StageExecutionResult>` - 执行处理函数
- `getRegisteredTypes(): string[]` - 获取所有已注册的stage类型
- `isHandlerRegistered(stageType: string): boolean` - 检查处理函数是否已注册

### useRegisterStageHandler(stageType, handler)

便捷的hook，用于注册stage组件处理函数。

**参数:**
- `stageType: string` - stage组件类型
- `handler: StageComponentHandler` - 处理函数

### StageComponentHandler

处理函数的类型定义。

```tsx
type StageComponentHandler = (action: ChatAction) => Promise<void> | void;
```

### StageExecutionResult

执行结果的类型定义。

```tsx
interface StageExecutionResult {
    success: boolean;
    error?: string;
    data?: any;
}
```

## 错误处理

- 如果指定的stage类型未注册，会返回错误信息
- 如果处理函数执行过程中抛出异常，会被捕获并返回错误信息
- 所有错误都会在控制台输出详细信息，便于调试

## 注意事项

1. 处理函数会在组件卸载时自动取消注册
2. 同一个stage类型只能注册一个处理函数，后注册的会覆盖前面的
3. 处理函数可以是同步或异步的
4. 建议在处理函数中进行适当的错误处理
5. stageParams可以传递任意类型的数据给处理函数
