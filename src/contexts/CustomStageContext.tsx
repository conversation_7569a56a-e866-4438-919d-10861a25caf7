import {createContext, useContext, useCallback, useMemo, ReactNode, useEffect, useState, FC} from 'react';
import {ChatAction} from '@/types/staff/element';

export type StageComponentHandler = (action: ChatAction) => Promise<void> | void;

export interface StageComponentHandlers {
    [stageType: string]: StageComponentHandler;
}

export interface StageExecutionResult {
    success: boolean;
    error?: string;
    data?: any;
}

interface CustomStageContextValue {
    registerHandler: (stageType: string, handler: StageComponentHandler) => void;
    unregisterHandler: (stageType: string) => void;
    executeHandler: (stageType: string, action: ChatAction) => Promise<StageExecutionResult>;
    getRegisteredTypes: () => string[];
    isHandlerRegistered: (stageType: string) => boolean;
}

const CustomStageContext = createContext<CustomStageContextValue | null>(null);

interface CustomStageProviderProps {
    children: ReactNode;
}

export const CustomStageProvider: FC<CustomStageProviderProps> = ({children}) => {
    const [handlers, setHandlers] = useState<StageComponentHandlers>({});

    const registerHandler = useCallback(
        (stageType: string, handler: StageComponentHandler) => {
            setHandlers(prev => ({
                ...prev,
                [stageType]: handler,
            }));
        },
        []
    );

    const unregisterHandler = useCallback(
        (stageType: string) => {
            setHandlers(prev => {
                const newHandlers = {...prev};
                delete newHandlers[stageType];
                return newHandlers;
            });
        },
        []
    );

    const executeHandler = useCallback(
        async (stageType: string, action: ChatAction): Promise<StageExecutionResult> => {
            const handler = handlers[stageType];
            if (!handler) {
                return {
                    success: false,
                    error: `未找到stage类型 "${stageType}" 对应的处理函数`,
                };
            }

            try {
                const result = await handler(action);
                return {
                    success: true,
                    data: result,
                };
            } catch (error) {
                const errorMessage = error instanceof Error ? error.message : String(error);
                console.error(`执行自定义stage组件处理函数失败 [${stageType}]:`, error);
                return {
                    success: false,
                    error: `执行处理函数失败: ${errorMessage}`,
                };
            }
        },
        [handlers]
    );

    const getRegisteredTypes = useCallback(
        () => {
            return Object.keys(handlers);
        },
        [handlers]
    );

    const isHandlerRegistered = useCallback(
        (stageType: string) => {
            return stageType in handlers;
        },
        [handlers]
    );

    const contextValue = useMemo(
        () => ({
            registerHandler,
            unregisterHandler,
            executeHandler,
            getRegisteredTypes,
            isHandlerRegistered,
        }),
        [registerHandler, unregisterHandler, executeHandler, getRegisteredTypes, isHandlerRegistered]
    );

    return (
        <CustomStageContext.Provider value={contextValue}>
            {children}
        </CustomStageContext.Provider>
    );
};

export const useCustomStage = (): CustomStageContextValue => {
    const context = useContext(CustomStageContext);
    if (!context) {
        throw new Error('useCustomStage必须在CustomStageProvider内部使用');
    }
    return context;
};

export const useRegisterStageHandler = (stageType: string, handler: StageComponentHandler) => {
    const {registerHandler, unregisterHandler} = useCustomStage();

    useEffect(
        () => {
            registerHandler(stageType, handler);
            return () => {
                unregisterHandler(stageType);
            };
        },
        [stageType, handler, registerHandler, unregisterHandler]
    );
};
