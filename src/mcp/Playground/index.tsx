import {Form, Splitter} from 'antd';
import {useEffect} from 'react';
import {Markdown} from '@/design/Markdown';
import {MCPTrackActions} from '@/api/mcp/track';
import {CustomStageProvider} from '@/contexts/CustomStageContext';
import ConfigPanel from './ConfigPanel';
import ChatArea from './ChatArea';


export default function MCPPlayground() {
    const [form] = Form.useForm();
    const mcpServers = Form.useWatch('mcpServers', form);

    const disabledReason = !mcpServers?.length
        ? '选择MCP Server后，即可开始对话'
        : undefined;

    useEffect(
        () => {
            MCPTrackActions.enterPlayground();
        },
        []
    );
    return (
        <CustomStageProvider>
            <Splitter style={{height: 'calc(100vh - 48px)'}}>
                <Splitter.Panel defaultSize={400} min="20%" max="40%">
                    <ConfigPanel form={form} />
                </Splitter.Panel>
                <Splitter.Panel min="60%" max="80%">
                    <ChatArea disabledReason={disabledReason} />
                </Splitter.Panel>
            </Splitter>
            {/* 首次挂载Markdown时会导致页面刷新，暂时先提前挂载一个隐藏的Markdown组件，避免表单被重新渲染引发Bug */}
            <Markdown content={null} codeHighlight={false} style={{display: 'none'}} />
        </CustomStageProvider>
    );
}
