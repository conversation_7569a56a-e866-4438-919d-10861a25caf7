import React from 'react';
import {ChatAction} from '@/types/staff/element';
import {useCustomStage} from '@/contexts/CustomStageContext';
import {ToolAction} from './ToolAction';

interface ToolActionWithCustomStageProps {
    action: ChatAction;
}

export const ToolActionWithCustomStage: React.FC<ToolActionWithCustomStageProps> = ({action}) => {
    // 直接调用hook，如果Context不可用会抛出错误
    const customStageContext = useCustomStage();
    const customStageExecutor = customStageContext.executeHandler;

    return <ToolAction action={action} customStageExecutor={customStageExecutor} />;
};

export default ToolActionWithCustomStage;
