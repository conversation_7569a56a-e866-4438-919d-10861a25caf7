import {message} from '@panda-design/components';
import {ChatAction} from '@/types/staff/element';

interface StageExecutionResult {
    success: boolean;
    error?: string;
    data?: any;
}

export const handleInPlatformAction = async (action: ChatAction): Promise<void> => {
    const {actionTypes = [], stageType, anchor} = action;

    if (!actionTypes.includes('inPlatform')) {
        return;
    }

    const targetStageType = stageType || anchor?.stageId;
    if (!targetStageType) {
        console.warn('inPlatform action缺少stageType或anchor.stageId');
        message.warning('缺少stage组件类型信息');
        return;
    }

    try {
        // 动态导入CustomStageContext以避免在不支持的环境中出错
        const {useCustomStage} = await import('@/contexts/CustomStageContext');

        // 这里我们需要在React组件上下文中调用这个函数
        // 由于这是一个普通函数，我们无法直接使用hooks
        // 所以我们需要通过全局状态或其他方式来访问Context
        console.warn('handleInPlatformAction需要在React组件上下文中调用');
        message.warning('自定义stage组件功能需要在支持的环境中使用');
    } catch (error) {
        console.warn('CustomStageContext不可用:', error);
        message.warning('自定义stage组件功能不可用');
    }
};
